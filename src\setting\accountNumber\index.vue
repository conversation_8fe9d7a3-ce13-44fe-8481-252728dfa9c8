<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="账户与安全中心"></CustomNavBar>
    <view class="setting">
      <view class="setting-list flex-between" @click="goRuleCenter">
        <view class="text-32rpx font-w-500">规则中心</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goAccountMange">
        <view class="text-32rpx font-w-500">账号管理</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goPermission">
        <view class="text-32rpx font-w-500">权限管理</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goLoginDevice">
        <view class="text-32rpx font-w-500">登陆设备管理</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const goRuleCenter = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/index',
  })
}
const goAccountMange = () => {
  uni.navigateTo({
    url: '/setting/accountMange/index',
  })
}
const goLoginDevice = () => {
  uni.navigateTo({
    url: '/setting/loginDevice/index',
  })
}
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
const goPermission = () => {
  uni.navigateTo({
    url: '/setting/permission/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}
</style>
