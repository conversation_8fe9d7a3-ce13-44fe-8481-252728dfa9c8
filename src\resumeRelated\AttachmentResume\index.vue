<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="附件简历"></CustomNavBar>
    </template>
    <view class="box">
      <view class="box-list">
        <view
          class="box-list-item flex-between m-b-30rpx"
          v-for="(item, index) in fileList"
          :key="index"
        >
          <view class="flex-c" @click="previewPdf(item)">
            <wd-img :width="38" :height="38" :src="pdf" />
            <view class="m-l-30rpx">
              <view class="text-28rpx c-#333333 p-b-10rpx">{{ item.fileName }}</view>
              <view class="text-24rpx c-#888888">{{ item.createTime }}</view>
            </view>
          </view>
          <wd-img :width="20" :height="20" :src="del" @click.stop="delpdf(item.id)"></wd-img>
        </view>
        <view class="box-list-item flex-between m-b-30rpx">
          <view class="text-28rpx c-#333333">上传新附件({{ fileList.length }}/3)</view>
          <view v-if="fileList.length >= 3"></view>
          <view v-else>
            <wd-img :width="20" :height="20" :src="addpdf" @click="showPdfUploadModal"></wd-img>
          </view>
        </view>
      </view>
    </view>

    <!-- PDF上传模态框 -->
    <view v-if="showUploadModal" class="upload-modal-overlay" @click="hidePdfUploadModal">
      <view class="upload-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">上传PDF文件</view>
          <view class="modal-close" @click="hidePdfUploadModal">×</view>
        </view>
        <view class="modal-content">
          <web-view
            v-if="webviewUrl"
            :src="webviewUrl"
            @message="handleWebViewMessage"
            @load="handleWebViewLoad"
            class="pdf-webview"
          />
          <view v-if="webviewLoading" class="loading-overlay">
            <view class="loading-content">
              <text class="loading-text">正在加载PDF上传页面...</text>
            </view>
          </view>
          <view v-if="webviewError" class="error-overlay">
            <view class="error-content">
              <text class="error-text">{{ webviewError }}</text>
              <button @click="retryLoad" class="retry-btn">重试</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="GenerateResume">生成简历附件</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import pdf from '@/resumeRelated/img/pdfimg.png'
import del from '@/resumeRelated/img/del.png'
import addpdf from '@/resumeRelated/img/addpdf.png'
import { queryMyFileResumeList, deleteFileResume, addFileResume } from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
import { useUserInfo } from '@/hooks/common/useUserInfo'
import { usePaging } from '@/hooks/common/usePaging'
import { onMounted } from 'vue'

const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const fileId = ref(null)
const message = useMessage()

// WebView相关状态
const showUploadModal = ref(false)
const webviewUrl = ref('')
const webviewLoading = ref(false)
const webviewError = ref('')

// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})

// 生成简历
const GenerateResume = () => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/GeneratePDF`,
  })
}

// PDF上传路径 - 使用图片上传端点，但支持PDF文件
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
console.log(baseUrl, '=========')

onShow(() => {
  getList()
})

onMounted(() => {
  // H5下监听iframe的postMessage
  window.addEventListener('message', (event) => {
    console.log('[父页面收到H5消息]', event.data)
    handleWebViewMessage({ detail: event.data })
  })
})

const fileList = ref([])

// 获取列表
const getList = async () => {
  console.log('[步骤] 获取文件列表...')
  const res: any = await queryMyFileResumeList()
  console.log('[步骤] 文件列表接口返回:', res)
  if (res.code === 0) {
    fileList.value = res.data
  }
}

// 查看pdf
const previewPdf = (item: any) => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewpdf?fileName=${item.fileName}&fileUrl=${item.fileUrl}`,
  })
}

// 删除
const delpdf = async (id: any) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      deleteFileResume({ id }).then((res: any) => {
        if (res.code === 0) {
          getList()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}

// 显示PDF上传模态框
const showPdfUploadModal = () => {
  console.log('[步骤] 点击上传PDF按钮，当前已上传数量：', fileList.value.length)
  if (fileList.value.length >= 3) {
    uni.showToast({ title: '最多上传3个附件', icon: 'none' })
    return
  }
  showUploadModal.value = true
  console.log('[步骤] 显示上传模态框')
  initWebView()
}

// 隐藏PDF上传模态框
const hidePdfUploadModal = () => {
  console.log('[步骤] 隐藏上传模态框')
  showUploadModal.value = false
  webviewUrl.value = ''
  webviewLoading.value = false
  webviewError.value = ''
}

// 初始化WebView
const initWebView = () => {
  try {
    console.log('[步骤] 初始化WebView')
    webviewLoading.value = true
    webviewError.value = ''

    // 使用静态HTML文件
    webviewUrl.value = '/static/pdf-upload.html'
    console.log('[步骤] 设置webviewUrl:', webviewUrl.value)
  } catch (error) {
    console.error('[步骤] 初始化WebView失败:', error)
    webviewError.value = '初始化失败，请重试'
    webviewLoading.value = false
  }
}

// 处理WebView消息
const handleWebViewMessage = (event: any) => {
  try {
    const message = event.detail?.data?.[0] || event.detail
    console.log('[步骤] 收到WebView消息:', message)

    if (!message || !message.type) {
      console.warn('[步骤] 无效的WebView消息:', message)
      return
    }

    switch (message.type) {
      case 'WEBVIEW_READY':
        console.log('[步骤] WebView已就绪')
        handleWebViewReady()
        break

      case 'FILE_SELECTED':
        console.log('[步骤] 文件已选择:', message.data)
        handleFileSelected(message.data)
        break

      case 'UPLOAD_PROGRESS':
        console.log('[步骤] 上传进度:', message.data)
        handleUploadProgress(message.data)
        break

      case 'UPLOAD_SUCCESS':
        console.log('[步骤] 上传成功:', message.data)
        handleUploadSuccess(message.data)
        break

      case 'UPLOAD_ERROR':
        console.log('[步骤] 上传失败:', message.data)
        handleUploadError(message.data)
        break

      case 'INIT_CONFIG':
        // 忽略从WebView返回的INIT_CONFIG消息，避免循环
        console.log('[步骤] 收到INIT_CONFIG确认消息')
        break

      default:
        console.warn('[步骤] 未知的消息类型:', message.type)
    }
  } catch (error) {
    console.error('[步骤] 处理WebView消息失败:', error)
  }
}

// 处理WebView加载完成
const handleWebViewLoad = () => {
  console.log('[步骤] WebView加载完成')
  webviewLoading.value = false
  // 移除重复的配置发送，只在handleWebViewReady中发送
}

// 处理WebView就绪
const handleWebViewReady = () => {
  console.log('[步骤] WebView就绪')
  // 延迟发送配置到WebView，确保页面完全加载
  setTimeout(() => {
    const config = {
      maxFileSize: 20 * 1024 * 1024, // 20MB
      allowedTypes: ['application/pdf'],
      uploadUrl: baseUrl,
      headers: header.value,
      timeout: 300000,
      additionalData: { type: 'resume' },
    }
    console.log('[步骤] 发送配置到WebView:', config)
    sendMessageToWebView({
      type: 'INIT_CONFIG',
      data: config,
    })
  }, 500)
}

// 处理文件选择
const handleFileSelected = (data: any) => {
  console.log('[步骤] 文件已选择:', data)
}

// 处理上传进度
const handleUploadProgress = (data: any) => {
  console.log('[步骤] 上传进度:', data.progress)
}

// 处理上传成功
const handleUploadSuccess = (data: any) => {
  console.log('[步骤] 上传成功:', data)

  // 关闭模态框
  hidePdfUploadModal()

  // 添加到文件列表
  if (data.fileId) {
    console.log('[步骤] 上传成功，fileId:', data.fileId)
    addFilePDF(data.fileId)
  } else if (data.data && data.data[0] && data.data[0].fileId) {
    console.log('[步骤] 上传成功，fileId:', data.data[0].fileId)
    addFilePDF(data.data[0].fileId)
  } else {
    console.log('[步骤] 上传成功但未获取到fileId')
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'error',
    })
  }
}

// 处理上传错误
const handleUploadError = (error: string) => {
  console.error('[步骤] 上传失败:', error)
  uni.showToast({
    title: error || '上传失败',
    icon: 'error',
  })
}

// 重试加载
const retryLoad = () => {
  console.log('[步骤] 重试加载WebView')
  webviewError.value = ''
  initWebView()
}

// 发送消息到WebView
const sendMessageToWebView = (message: any) => {
  console.log('[步骤] 发送消息到WebView:', message)

  try {
    // 通过window.postMessage发送消息
    window.postMessage(message, '*')
  } catch (error) {
    console.error('[步骤] 发送消息到WebView失败:', error)
  }
}

// 上传
const addFilePDF = async (fileId: string) => {
  console.log('[步骤] 调用addFilePDF，fileId:', fileId)
  const res: any = await addFileResume({ fileId })
  console.log('[步骤] addFileResume接口返回:', res)
  if (res.code === 0) {
    getList()
    uni.showToast({ title: '上传成功', icon: 'success' })
  } else {
    uni.showToast({ title: res.msg || '上传失败', icon: 'none' })
  }
}

const addPdfImg3 = () => {
  uni.showToast({ title: '最多上传3个附件', icon: 'none' })
}
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}

// 上传模态框样式
.upload-modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.upload-modal {
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
  background: white;
  border-radius: 20rpx;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  padding: 10rpx;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-content {
  position: relative;
  flex: 1;
  min-height: 600rpx;
}

.pdf-webview {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.error-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.95);
}

.error-content {
  padding: 40rpx;
  text-align: center;
}

.error-text {
  display: block;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  color: #ff4757;
}

.retry-btn {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: white;
  background-color: #007aff;
  border: none;
  border-radius: 10rpx;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
