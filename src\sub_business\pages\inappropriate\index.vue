<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    @query="queryList"
    :paging-style="pageStyle"
    safe-area-inset-bottom
  >
    <template #top>
      <CustomNavBar title="不合适"></CustomNavBar>
    </template>
    <view>
      <wd-checkbox-group v-model="ids">
        <view
          class="flex items-baseline justify-between border-b border-#E0E0E0 border-b-solid"
          v-for="(item, index) in pageData"
          :key="index"
        >
          <mylist :list="item" />
          <wd-checkbox :modelValue="item.id" class="m-r-20rpx"></wd-checkbox>
        </view>
      </wd-checkbox-group>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="removeUnInt">
          <view class="btn_bg">移出不合适</view>
        </view>
      </view>
    </template>
  </z-paging>
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { unInterestList, removeUnInterest } from '@/service/hrBiographical'
import mylist from '@/sub_business/component/mylist.vue'
import { getCustomBar } from '@/utils/storage'
const customBar = ref(null)
const { pageInfo, pageSetInfo, pageData, pageStyle, pagingRef } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const message = useMessage()
const list = ref({})
const params = reactive({
  entity: {},
  orderBy: {},
})
const ids = ref([])
const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  const res: any = await unInterestList({
    ...params,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    res.data?.list.forEach((item, index) => {
      item.id = index + 1
    })
    pagingRef.value.complete(res.data?.list)
  }
}
// 移出不合适
const removeUnInt = async () => {
  if (ids.value.length === 0) {
    uni.showToast({
      title: '请选择',
      icon: 'none',
    })
    return
  }
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      removeUnInterest({
        ids: ids.value,
      }).then((res: any) => {
        if (res.code === 0) {
          ids.value = []
          pagingRef.value.reload()
        }
      })
    })
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
:deep(.wd-checkbox-group) {
  background: transparent;
}
.btn-fixed {
  padding: 40rpx 60rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
