<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="面试"></CustomNavBar>
    <view class="page-time flex items-center p-l-40rpx p-r-40rpx p-t-40rpx">
      <view class="text-100rpx font-600 c-#000">{{ currentDate.slice(5, 7) }}</view>
      <view>
        <view class="text-32rpx c-#555">月</view>
        <view class="text-32rpx c-#555">{{ currentDate.slice(0, 4) }}年</view>
      </view>
    </view>

    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view class="m-l-40rpx m-r-40rpx">
        <scroll-view ref="dateScrollRef" scroll-x class="white-space-nowrap w-[calc(100% - 80rpx)]">
          <view class="gap-60rpx flex items-center">
            <view
              v-for="(item, index) in monthDays"
              :key="index"
              :id="'date-' + index"
              class="text-28rpx text-center"
              :class="selectedIndex === index ? 'date-selected' : ''"
              style="min-width: 80rpx; padding: 10rpx 0; cursor: pointer"
              @click="selectDate(index)"
            >
              <view>{{ item.week }}</view>
              <view>{{ item.date.slice(8, 10) }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="pageList m-t-40rpx">
        <view class="c-#888 text-32rpx p-b-20rpx p-l-40rpx" v-if="pageData.length > 0">时间</view>
        <view class="pageList-item flex items-start" v-for="(item, index) in pageData" :key="index">
          <view class="pageList-item-left">
            <view class="c-#000 text-34rpx text-right">{{ item.agreeTime.slice(11, 13) }}点</view>
          </view>
          <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
            <view class="pageList-item-right-card flex items-center">
              <view class="w-90rpx">
                <image
                  class="w-76rpx h-76rpx rounded-full"
                  v-if="item.sex === 1"
                  :src="item.headImgUrl ? item.headImgUrl : '/static/header/jobhunting1.png'"
                  mode="aspectFill"
                ></image>
                <image
                  v-else
                  class="w-76rpx h-76rpx rounded-full"
                  :src="item.headImgUrl ? item.headImgUrl : '/static/header/jobhunting2.png'"
                  mode="aspectFill"
                ></image>
              </view>

              <view class="flex-1">
                <view class="c-#fff text-28rpx p-b-5rpx u-line-1 w-340rpx">
                  {{ item.name }}
                </view>
                <view class="flex justify-between">
                  <view class="c-#fff text-24rpx">{{ item.positionName }}</view>
                  <view class="c-#fff text-24rpx">
                    {{ item.workSalaryStart }}-{{ item.workSalaryEnd }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryWaitMeetingList } from '@/interPost/resume'
import { getCustomBar } from '@/utils/storage'
import { getMonthDays, formatDateDay } from '@/utils/common'
import { ref, computed, onMounted, nextTick, watch } from 'vue'
const { pagingRef, pageData, pageStyle } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '20rpx',
  },
})
// 当前的月份对应的日期和星期
const monthDays = ref([])
// 当前日期
const currentDate = ref('')
const customBar = ref(null)
// 新增：高亮选中索引
const selectedIndex = ref(0)
const scrollToId = computed(() => 'date-' + selectedIndex.value)
const dateScrollRef = ref()

function scrollDateToCenter(idx) {
  nextTick(() => {
    const query = uni.createSelectorQuery().in(dateScrollRef.value)
    query.select(`#date-${idx}`).boundingClientRect()
    query.select('.white-space-nowrap').boundingClientRect()
    query.exec((res) => {
      const itemRect = res[0]
      const scrollRect = res[1]
    })
  })
}

function selectDate(idx) {
  selectedIndex.value = idx
  scrollDateToCenter(idx)
}

// 监听monthDays，数据变化后自动高亮今日并居中
watch(
  monthDays,
  (val) => {
    if (val && val.length > 0) {
      const todayIdx = val.findIndex((item) => item.isToday)
      selectedIndex.value = todayIdx !== -1 ? todayIdx : 0
      scrollDateToCenter(selectedIndex.value)
    }
  },
  { immediate: true },
)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async () => {
  const res: any = await queryWaitMeetingList()
  if (res.code === 0) {
    pagingRef.value.complete(res.data)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  monthDays.value = getMonthDays()
  console.log(monthDays.value, 'monthDays==========')
  currentDate.value = formatDateDay()
  pagingRef.value.setLocalPaging([
    {
      agreeTime: '2025-07-08 10:00:00',
      id: 1,
      name: '张三',
      positionName: '前端工程师',
      workSalaryStart: 10000,
      workSalaryEnd: 20000,
      sex: 1,
      headImgUrl: '/static/header/jobhunting1.png',
    },
    {
      agreeTime: '2025-07-08 10:00:00',
      id: 1,
      name: '张三',
      positionName: '前端工程师',
      workSalaryStart: 10000,
      workSalaryEnd: 20000,
      sex: 1,
      headImgUrl: '/static/header/jobhunting1.png',
    },
  ])
  // pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  background: #4399ff;
  border-radius: 20rpx;
}
.date-selected {
  color: #fff !important;
  background: orange !important;
  border-radius: 20rpx;
}
</style>
