<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-navbar
        :bordered="false"
        safe-area-inset-top
        placeholder
        custom-class="!bg-transparent"
        @click-left="handleNavLeft"
      >
        <template #left>
          <wd-icon name="arrow-left" size="30rpx" color="#333333" />
        </template>
        <template #title>
          <text class="c-#333333 text-32rpx font-500">{{ stepsInfo.title }}</text>
        </template>
      </wd-navbar>
    </template>
    <view class="component-wrapper" :class="{ 'is-transitioning': isTransitioning }">
      <component
        :is="stepsInfo?.component"
        v-bind="stepsInfo?.props ?? {}"
        :key="releaseCurrentStep"
        ref="currentStepRef"
        @next-step="goToStep"
        :class="['component-content', animationClass]"
      />
    </view>
    <template #bottom>
      <view class="center flex-col pb-94rpx gap-30rpx">
        <wd-button
          :round="false"
          custom-class="w-590rpx !h-100rpx !bg-transparent !bg-gradient-to-r from-[#FFC2C2] to-[#DDDCFF] !rounded-28rpx"
          @click="handleActions"
        >
          <text class="c-#333333 text-28rpx font-500">{{ stepsInfo?.butText ?? '下一步' }}</text>
        </wd-button>
        <wd-button
          :round="false"
          custom-class="w-590rpx !h-100rpx !bg-#D9D9D9 !rounded-28rpx"
          v-if="stepsInfo.step === ReleaseStep.DESCRIBE"
        >
          <text class="c-#333333 text-28rpx font-500">保存</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import type { Component } from 'vue'
import { EMIT_EVENT } from '@/enum'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { ReleaseStep, ReleasePositionInfo } from '@/sub_business/types/release'
import { hrPositionAdd, hrPositionPayAndPublish } from '@/service/hrPosition'
import { getCurrentMonth } from '@/utils'
import release from './module/release.vue'
import position from './module/position.vue'
import describe from './module/describe.vue'

interface StepItem {
  step: ReleaseStep
  title: string
  component: Component
  butText?: string
  props?: Record<string, any>
  actions?: () => void
}

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const { releasePostModel, releaseSubmitPostModel, releaseCurrentStep, resetReleasePostModel } =
  useReleasePost()
const currentStepRef = ref()
const STEP_CONFIGS: StepItem[] = [
  {
    step: ReleaseStep.RELEASE,
    title: '发布职位',
    component: release,
    actions: async () => {
      try {
        if (currentStepRef.value && typeof currentStepRef.value.releaseValidate === 'function') {
          const { valid } = await currentStepRef.value.releaseValidate()
          if (valid) {
            goToStep(ReleaseStep.DESCRIBE)
          }
        }
      } catch (error) {}
    },
  },
  {
    step: ReleaseStep.SALARY,
    title: '职位待遇',
    component: position,
    butText: '完成',
    props: {
      type: ReleasePositionInfo.SALARY,
    },
    actions: () => {
      currentStepRef.value?.submitData?.()
      handleNavLeft()
    },
  },
  {
    step: ReleaseStep.KEYWORDS,
    title: '职位关键词',
    component: position,
    butText: '完成',
    props: {
      type: ReleasePositionInfo.KEYWORDS,
    },
    actions: () => {
      currentStepRef.value?.submitData?.()
      handleNavLeft()
    },
  },
  {
    step: ReleaseStep.DESCRIBE,
    title: '职位描述',
    component: describe,
    butText: '付费发布',
    actions: async () => {
      if (!releasePostModel.value.positionDesc) {
        uni.showToast({
          title: '请填写职位描述',
          icon: 'none',
        })
        return
      }
      uni.showLoading({
        title: '发布中...',
      })
      try {
        const { data } = await hrPositionAdd(releaseSubmitPostModel.value, {
          custom: {
            catch: true,
          },
        })
        await hrPositionPayAndPublish(
          {
            positionId: data,
            publishMonth: getCurrentMonth(),
          },
          {
            custom: {
              catch: true,
            },
          },
        )
        uni.hideLoading()
        uni.showToast({
          title: '发布成功',
          icon: 'none',
        })
        setTimeout(() => {
          resetReleasePostModel()
          uni.$emit(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
          uni.navigateBack()
        }, 1000)
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: error?.msg || '发布失败',
          icon: 'none',
        })
      }
    },
  },
]
const stepHistory = ref<number[]>([])
const isTransitioning = ref(false)
const animationClass = ref('')

const stepsInfo = computed(() => {
  const stepConfig = STEP_CONFIGS.find((item) => item.step === releaseCurrentStep.value)
  return stepConfig
})
function handleActions() {
  const stepConfig = stepsInfo.value
  stepConfig?.actions()
}
function goToStep(step: number) {
  stepHistory.value.push(releaseCurrentStep.value)

  isTransitioning.value = true
  animationClass.value = 'slide-out'

  setTimeout(() => {
    releaseCurrentStep.value = step
    animationClass.value = 'slide-in'

    setTimeout(() => {
      isTransitioning.value = false
      animationClass.value = ''
    }, 300)
  }, 150)
}

function handleNavLeft() {
  if (releaseCurrentStep.value === ReleaseStep.RELEASE) {
    uni.navigateBack()
    return
  }

  const previousStep = stepHistory.value.pop() ?? ReleaseStep.RELEASE

  isTransitioning.value = true
  animationClass.value = 'slide-out-reverse'

  setTimeout(() => {
    releaseCurrentStep.value = previousStep
    animationClass.value = 'slide-in-reverse'

    setTimeout(() => {
      isTransitioning.value = false
      animationClass.value = ''
    }, 300)
  }, 150)
}
</script>

<style lang="scss" scoped>
.component-wrapper {
  position: relative;
  overflow: hidden;
}

.component-content {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

  &.slide-in {
    animation: slideIn 0.3s ease-out forwards;
  }

  &.slide-out {
    animation: slideOut 0.15s ease-in forwards;
  }

  &.slide-in-reverse {
    animation: slideInReverse 0.3s ease-out forwards;
  }

  &.slide-out-reverse {
    animation: slideOutReverse 0.15s ease-in forwards;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50rpx);
  }
}

@keyframes slideInReverse {
  from {
    opacity: 0;
    transform: translateX(-100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutReverse {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(50rpx);
  }
}
</style>
