import { changeToBAuth, changeToCAuth } from '@/interPost/common'
import { USER_TYPE } from '@/enum'

export const useChangeIdent = () => {
  const { userIntel, setUserIntel } = useUserInfo()
  const { newInfoStepPage } = useNewInfoAll()

  const changeIdent = async () => {
    try {
      const identConfig = {
        [USER_TYPE.APPLICANT]: { authFn: changeToBAuth, type: USER_TYPE.HR },
        [USER_TYPE.HR]: { authFn: changeToCAuth, type: USER_TYPE.APPLICANT },
      }
      const config = identConfig[userIntel.value.type]
      const { data } = await config.authFn()
      setUserIntel({ ...data, type: config.type })
      newInfoStepPage(true, data.requiredFinishStatus)
    } catch (error) {}
  }

  return {
    changeIdent,
  }
}
