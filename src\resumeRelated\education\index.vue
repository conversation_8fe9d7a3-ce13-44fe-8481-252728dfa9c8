<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="教育经历">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="mainText labelName">学校</view>
        <view class="flex-c">
          <view
            class="text-white-space"
            :class="fromData.school ? 'selelctColor' : 'nomalColor'"
            @click="goSchool"
          >
            {{ fromData.school ? fromData.school : '学校名称' }}
          </view>
          <wd-icon
            @click="goSchool"
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">学历</view>
        <wd-picker :columns="columnsList" v-model="fromData.qualification" />
        <!-- <view class="text-34rpx font-w-500 flex-1 text-r">研究生·全日制</view> -->
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">专业</view>
        <view class="flex-c">
          <!-- <wd-input no-border v-model="fromData.major" @click="goMajor" placeholder="专业名称" /> -->
          <view :class="fromData.major ? 'c-#333333' : 'c-#888888'" @click="goMajor">
            {{ fromData.major ? fromData.major : '专业名称' }}
          </view>

          <!-- <view class="m-l-40rpx"></view> -->
          <wd-icon
            @click="goMajor"
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">起止时间</view>
        <view class="flex-c">
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.startTime ? 'c-#333333' : 'c-#888888'"
            @click="showDateRangePicker"
          >
            {{ fromData.startTime ? fromData.startTime + '至' + fromData.endTime : '请选择' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>

        <!-- <view class="mainText labelName" @click="showDateRangePicker">选择时间段</view> -->
        <!-- <wd-datetime-picker v-model="timeData" @confirm="handleConfirm" type="year" /> -->
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delSubmit"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
    <date-range-picker
      ref="dateRangePicker"
      v-model="dateRange"
      @confirm="onDateRangeConfirm"
      @cancel="onDateRangeCancel"
    />
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import DateRangePicker from '@/components/range-picker/range-picker.vue'
import { useResumeStore } from '@/store'
import {
  resumeEducationalAdd,
  resumeEducationalUpdate,
  resumeEducationalDel,
} from '@/interPost/resume'
import { DICT_IDS } from '@/enum'
import isEqual from 'lodash/isEqual'
import { useMessage } from 'wot-design-uni'
const { getDictData } = useDictionary()

// z-paging配置
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const isAdd = ref(null)
const resumeStore = useResumeStore()
const objItem = ref(null)
const timeData = ref([])
const baseInfoId = ref([])
const message = useMessage()

const dateRangePicker = ref(null)
const dateRange = ref([])

const showDateRangePicker = () => {
  dateRangePicker.value.open()
}

const onDateRangeConfirm = (e) => {
  fromData.value.startTime = e.value[0]
  fromData.value.endTime = e.value[1]
}

const onDateRangeCancel = () => {
  uni.showToast({
    title: '已取消选择',
    icon: 'none',
  })
}
// 表单
const fromData = ref({
  endTime: '',
  major: '',
  qualification: undefined as number | undefined, // 改为数字类型
  school: '',
  startTime: '',
})
// 表单初始化 - 将在数据加载完成后设置
const fromDataInit = ref({})
// 学历
const columnsList = ref([])

// 字典

// 确认
const submit = async () => {
  console.log('====================')
  if (!fromData.value.school) {
    uni.showToast({
      title: '请输入学校名称',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.qualification) {
    uni.showToast({
      title: '请选择学历',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.major) {
    uni.showToast({
      title: '请输入专业',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.startTime) {
    uni.showToast({
      title: '请选择时间段',
      icon: 'none',
    })
    return
  }
  if (isAdd.value === 'add') {
    const res: any = await resumeEducationalAdd({ ...fromData.value, baseInfoId: baseInfoId.value })
    console.log(res, 'res')
    if (res.code === 0) {
      resumeStore.setSchool('')
      resumeStore.setMajor('')
      resumeStore.setMajor('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeEducationalUpdate(fromData.value)
    console.log(res, 'res')
    if (res.code === 0) {
      resumeStore.setSchool('')
      resumeStore.setMajor('')
      resumeStore.setMajor('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
// 去学校
const goSchool = () => {
  uni.navigateTo({
    url: '/resumeRelated/education/school?school=' + fromData.value.school,
  })
}

// 专业
const goMajor = () => {
  uni.navigateTo({
    url: '/resumeRelated/education/major?major=' + fromData.value.major,
  })
}

// 处理学历类型匹配
const handleSchoolTypeMatch = () => {
  if (columnsList.value && columnsList.value.length > 0 && resumeStore.schoolType) {
    const str = columnsList.value.filter((item) => item.label === resumeStore.schoolType)
    console.log(columnsList.value, str, ' fromData.value.qualification=====')
    // 如果找到匹配的学历类型，可以在这里设置到 fromData.value.qualification
    if (str.length > 0) {
      fromData.value.qualification = Number(str[0].value) // 赋值时转为数字
    }
  }
}

onLoad(async (options) => {
  // 简历id
  baseInfoId.value = options.id
  isAdd.value = options.isAdd
  const res: any = await getDictData(DICT_IDS.EDUCATION_REQUIREMENT)
  const resData = res || res.data
  columnsList.value = Object.entries(resData)
    .map(([key, value]) => ({
      value: Number(key),
      label: value,
    }))
    .filter((ele) => ele.label !== '不限')

  if (isAdd.value === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    fromData.value = JSON.parse(decodeURIComponent(options.item))
    timeData.value = [fromData.value.startTime, fromData.value.endTime]
  }

  // 在数据加载完成后处理学历类型匹配
  handleSchoolTypeMatch()

  // 在所有数据加载完成后，设置初始化数据用于比较
  await nextTick()
  fromDataInit.value = JSON.parse(JSON.stringify(fromData.value))
})
// 删除
const delSubmit = () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除该条信息吗?',
    })
    .then(() => {
      resumeEducationalDel({ id: objItem.value.id, baseInfoId: baseInfoId.value }).then(
        (res: any) => {
          if (res.code === 0) {
            resumeStore.setSchool('')
            resumeStore.setMajor('')
            resumeStore.setSchoolType('')
            uni.navigateBack()
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 3000,
            })
          }
        },
      )
    })
}
// 判断表单是否全为空
function isFormEmpty(form) {
  return Object.values(form).every((v) => v === '' || v === null || v === undefined)
}
// 返回
const back = () => {
  if (isAdd.value === 'add') {
    if (isFormEmpty(fromData.value)) {
      resumeStore.setSchool('')
      resumeStore.setSchoolType('')
      resumeStore.setMajor('')
      uni.navigateBack()
    } else if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setSchool('')
      resumeStore.setSchoolType('')
      resumeStore.setMajor('')
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setSchool('')
          resumeStore.setSchoolType('')
          resumeStore.setMajor('')
          uni.navigateBack()
        })
    }
  } else {
    console.log('000000')
    if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setSchool('')
      resumeStore.setSchoolType('')
      resumeStore.setMajor('')
      uni.navigateBack()
    } else {
      message
        .confirm({
          title: '提示',
          msg: '您有内容未提交保存,确认返回吗?',
        })
        .then(() => {
          resumeStore.setSchool('')
          resumeStore.setSchoolType('')
          resumeStore.setMajor('')
          uni.navigateBack()
        })
    }
  }
}
onUnmounted(() => {
  resumeStore.setSchool('')
  resumeStore.setSchoolType('')
  resumeStore.setMajor('')
})
onShow(() => {
  fromData.value.school = resumeStore.school ? resumeStore.school : fromData.value.school
  // 处理学历类型匹配
  handleSchoolTypeMatch()

  if (isAdd.value === 'add') {
    fromData.value.major = resumeStore.major
  } else {
    fromData.value.major = resumeStore.major
      ? resumeStore.major
      : objItem.value
        ? objItem.value.major
        : fromData.value.major
  }
})
</script>

<style lang="scss" scoped>
::v-deep .wd-picker__cell {
  padding-right: 0rpx;
  background: transparent;
}
::v-deep .wd-picker__region.is-active {
  background: transparent;
}
::v-deep .wd-picker__value {
  font-size: 32rpx;
  color: #333333;
}
::v-deep .uni-input-input {
  font-size: 32rpx;
  color: #333333;
}
::v-deep .wd-picker__placeholder {
  font-size: 32rpx;
  color: #888888;
}
::v-deep .wd-picker__value {
  margin-right: 0rpx;
  font-size: 32rpx;
  // color: #333333;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx;
  color: #888888 !important;
}
::v-deep .wd-picker__value {
  // font-size: 32rpx;
  // font-weight: 500;
}
::v-deep .wd-picker__arrow {
  color: #888888;
}
.selelctColor {
  color: #333333;
}

.nomalColor {
  color: #888888;
}
::v-deep .wd-input {
  text-align: right;
  background-color: transparent;
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.labelName {
  width: 200rpx;
  text-align: left;
}

.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
