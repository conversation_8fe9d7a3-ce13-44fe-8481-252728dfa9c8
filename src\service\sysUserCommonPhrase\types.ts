export interface sysUserCommonPhraseQueryListDataInt {
  content?: string
}
export interface sysUserCommonPhraseQueryListInt {
  /** 常用语内容 */
  content: string
  /** 通知消息表主键id */
  id: number
  /** 排序值升序 */
  sortNo: number
  /** 系统缺省key */
  systemDefaultKey: string
}

export interface sysUserCommonPhraseDeleteByIdDataInt
  extends Pick<sysUserCommonPhraseQueryListInt, 'id'> {}

export interface sysUserCommonPhraseReSortDataInt {
  ids: number[]
}
