<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF上传</title>
    <script>
      // 立即执行的调试信息
      console.log('PDF上传页面开始加载')
      console.log('当前时间:', new Date().toISOString())
      console.log('window对象:', window)
      console.log('document对象:', document)
      console.log('location:', window.location.href)
    </script>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #333;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 600px;
        padding: 80px 20px 0px;
        margin: 0rpx auto 40rpx;
      }

      .upload-area {
        padding: 0px 20px;
        margin-top: 40rpx;
        margin-bottom: 20px;
        text-align: center;
        cursor: pointer;
        background: white;
        border: 2px dashed #007aff;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .upload-area:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
      }

      .upload-icon {
        padding-top: 10px;
        margin-bottom: 10px;
        font-size: 16px;
        color: #007aff;
      }

      .upload-text {
        margin-bottom: 8px;
        font-size: 12px;
        color: #666;
      }

      .upload-hint {
        font-size: 14px;
        color: #999;
      }

      .file-input {
        position: fixed;
        bottom: 20rpx;
        display: none;
      }

      .file-info {
        display: none;
        padding: 10px;
        /* margin-bottom: 20px; */
        background: white;
        border-radius: 8px;
      }

      .file-info.show {
        display: block;
      }

      .file-name {
        margin-top: 40rpx;
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
      }

      .file-size {
        font-size: 14px;
        color: #666;
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        margin-top: 12px;
        overflow: hidden;
        background-color: #e9ecef;
        border-radius: 2px;
      }

      .progress-fill {
        width: 0%;
        height: 100%;
        background-color: #007aff;
        border-radius: 2px;
        transition: width 0.3s ease;
      }

      .upload-btn {
        position: fixed;
        bottom: 40px;
        display: none;
        width: calc(100% - 40px);
        padding: 12px 20px;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background-color: #007aff;
        border: none;
        border-radius: 8px;
        transition: background-color 0.3s ease;
      }

      .upload-btn:hover {
        background-color: #0056b3;
      }

      .upload-btn:disabled {
        cursor: not-allowed;
        background-color: #6c757d;
      }

      .error-message {
        display: none;
        padding: 12px;
        margin-bottom: 20px;
        color: #721c24;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
      }

      .error-message.show {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="upload-area" id="uploadArea">
        <div class="upload-icon">📄</div>
        <div class="upload-text">点击上传PDF文件,最大20MB</div>
        <input
          type="file"
          id="fileInput"
          class="file-input"
          accept=".pdf,application/pdf"
          capture="false"
        />
      </div>

      <div class="error-message" id="errorMessage"></div>

      <div class="file-info" id="fileInfo">
        <div class="file-name" id="fileName"></div>
        <div class="file-size" id="fileSize"></div>
        <!-- <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div> -->
      </div>

      <button class="upload-btn" id="uploadBtn">上传文件</button>
    </div>

    <script>
      // 全局变量
      let selectedFile = null
      let uploadConfig = null

      // DOM元素
      const uploadArea = document.getElementById('uploadArea')
      const fileInput = document.getElementById('fileInput')
      const fileInfo = document.getElementById('fileInfo')
      const fileName = document.getElementById('fileName')
      const fileSize = document.getElementById('fileSize')
      const progressFill = document.getElementById('progressFill')
      const uploadBtn = document.getElementById('uploadBtn')
      const errorMessage = document.getElementById('errorMessage')

      // 统一更新上传按钮状态
      function updateUploadBtnStatus() {
        const hasFile = !!selectedFile
        const hasConfig = !!(uploadConfig && uploadConfig.uploadUrl)
        const shouldEnable = hasFile && hasConfig

        console.log('updateUploadBtnStatus:', {
          selectedFile: !!selectedFile,
          uploadConfig: !!uploadConfig,
          uploadUrl: uploadConfig && uploadConfig.uploadUrl,
          shouldEnable,
        })

        uploadBtn.disabled = !shouldEnable

        // 更新按钮文本
        if (uploadBtn.disabled) {
          uploadBtn.textContent = '上传文件'
          uploadBtn.style.backgroundColor = '#007aff'
        }
      }

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        console.log('DOMContentLoaded事件触发')
        initEventListeners()
        notifyUniAppReady()
        // 初始化时更新按钮状态
        updateUploadBtnStatus()
      })

      // 初始化事件监听器
      function initEventListeners() {
        console.log('初始化事件监听器')
        // 点击上传区域
        uploadArea.addEventListener('click', () => {
          console.log('上传区域被点击')
          fileInput.setAttribute('capture', 'false')
          fileInput.click()
        })
        // 文件选择
        fileInput.addEventListener('change', handleFileSelect)
        // 重新绑定上传按钮事件
        uploadBtn.onclick = function (event) {
          console.log('上传按钮被点击', event)
          console.log('按钮状态:', uploadBtn.disabled)
          console.log('按钮文本:', uploadBtn.textContent)

          if (!selectedFile) {
            showError('请先选择文件')
            console.error('没有选择文件')
            updateUploadBtnStatus()
            return
          }
          if (!uploadConfig || !uploadConfig.uploadUrl) {
            showError('上传配置错误: 缺少上传URL')
            console.error('上传配置错误:', uploadConfig)
            updateUploadBtnStatus()
            return
          }
          // 重置进度条
          updateProgress(0)
          uploadBtn.disabled = true
          uploadBtn.textContent = '上传中...'
          console.log('开始上传文件，准备调用uploadFile')
          uploadFile(selectedFile)
        }

        // 添加额外的点击事件监听
        uploadBtn.addEventListener('click', function (event) {
          console.log('上传按钮addEventListener被触发', event)
        })
        console.log('上传按钮事件监听器已添加')
        // 添加更多调试信息
        console.log('uploadArea元素:', uploadArea)
        console.log('fileInput元素:', fileInput)
        console.log('uploadBtn元素:', uploadBtn)
      }

      // 处理文件选择
      function handleFileSelect(event) {
        console.log('文件选择事件触发:', event)
        console.log('事件目标:', event.target)
        console.log('文件列表:', event.target.files)

        const file = event.target.files[0]
        console.log('选择的文件:', file)

        if (file) {
          console.log('文件存在，开始验证')
          validateAndSetFile(file)
        } else {
          console.log('没有选择文件')
        }
      }

      // 验证并设置文件
      function validateAndSetFile(file) {
        console.log('验证文件:', file)
        console.log('文件类型:', file.type)
        console.log('文件大小:', file.size)
        console.log('文件名称:', file.name)

        // 验证文件类型
        if (file.type !== 'application/pdf') {
          showError('请选择PDF格式的文件')
          console.error('文件类型不匹配:', file.type, '期望: application/pdf')
          uploadBtn.disabled = true
          return
        }

        // 验证文件大小
        const maxSize = uploadConfig?.maxFileSize || 10 * 1024 * 1024 // 默认10MB
        console.log('最大文件大小:', maxSize)
        console.log('当前文件大小:', file.size)

        if (file.size > maxSize) {
          showError('文件大小超过限制')
          console.error('文件大小超限:', file.size, '>', maxSize)
          uploadBtn.disabled = true
          return
        }

        selectedFile = file
        displayFileInfo(file)
        hideError()
        // 每次选文件后都重新校验按钮状态
        updateUploadBtnStatus()

        console.log('文件验证通过，已设置selectedFile')

        // 通知UniApp
        const messageData = {
          type: 'FILE_SELECTED',
          data: {
            name: file.name,
            size: file.size,
            type: file.type,
          },
        }
        console.log('发送文件选择消息:', messageData)
        sendMessageToUniApp(messageData)
      }

      // 显示文件信息
      function displayFileInfo(file) {
        console.log('显示文件信息:', file)
        fileName.textContent = file.name
        fileSize.textContent = formatFileSize(file.size)
        fileInfo.classList.add('show')
        console.log('文件信息已显示')
      }

      // 格式化文件大小
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      // 处理上传
      function handleUpload() {
        console.log('上传按钮被点击')
        console.log('selectedFile:', selectedFile)
        console.log('uploadConfig:', uploadConfig)

        if (!selectedFile) {
          showError('请先选择文件')
          console.error('没有选择文件')
          uploadBtn.disabled = false
          return
        }

        if (!uploadConfig || !uploadConfig.uploadUrl) {
          showError('上传配置错误: 缺少上传URL')
          console.error('上传配置错误:', uploadConfig)
          uploadBtn.disabled = false
          return
        }

        // 重置进度条
        updateProgress(0)
        uploadBtn.disabled = true
        uploadBtn.textContent = '上传中...'

        console.log('开始上传文件，准备调用uploadFile')
        uploadFile(selectedFile)
      }

      // 上传文件
      function uploadFile(file) {
        console.log('=== 开始上传文件 ===')
        console.log('文件对象:', file)
        console.log('文件名称:', file?.name)
        console.log('文件大小:', file?.size)
        console.log('文件类型:', file?.type)

        if (!file) {
          console.error('uploadFile参数file为空')
          uploadBtn.disabled = false
          return
        }
        if (!uploadConfig || !uploadConfig.uploadUrl) {
          console.error('uploadConfig或uploadUrl为空')
          console.error('uploadConfig:', uploadConfig)
          uploadBtn.disabled = false
          return
        }
        console.log('开始上传文件:', file)
        console.log('上传URL:', uploadConfig.uploadUrl)
        console.log('请求头:', uploadConfig.headers)
        console.log('超时设置:', uploadConfig.timeout)

        const formData = new FormData()
        formData.append('file', file)
        console.log('FormData已创建，文件已添加到FormData')

        // 检查FormData内容
        for (let [key, value] of formData.entries()) {
          console.log('FormData内容:', key, value)
        }

        const xhr = new XMLHttpRequest()
        console.log('XMLHttpRequest已创建')

        // 进度监听
        xhr.upload.addEventListener('progress', function (event) {
          console.log('进度事件触发:', event)
          console.log(
            '已加载:',
            event.loaded,
            '总大小:',
            event.total,
            '可计算:',
            event.lengthComputable,
          )

          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            console.log('计算进度:', progress + '%')
            updateProgress(progress)

            sendMessageToUniApp({
              type: 'UPLOAD_PROGRESS',
              data: { progress },
            })
          } else {
            console.log('进度不可计算，但已加载:', event.loaded)
            // 即使不可计算，也尝试更新进度
            if (event.loaded > 0) {
              updateProgress(10) // 显示一些进度
            }
          }
        })

        // 完成监听
        xhr.addEventListener('load', function () {
          console.log('上传响应状态:', xhr.status)
          console.log('上传响应文本:', xhr.responseText)

          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              console.log('解析的响应:', response)
              handleUploadSuccess(response)
            } catch (error) {
              console.error('响应解析失败:', error)
              handleUploadError('响应解析失败')
            }
          } else {
            console.error('上传失败，状态码:', xhr.status)
            handleUploadError('上传失败: ' + xhr.status)
          }
        })

        // 错误监听
        xhr.addEventListener('error', function (error) {
          console.error('上传网络错误:', error)
          handleUploadError('网络错误')
        })

        // 超时监听
        xhr.addEventListener('timeout', function () {
          console.error('上传超时')
          handleUploadError('上传超时')
        })

        // 发送请求
        console.log('准备打开XHR请求...')
        xhr.open('POST', uploadConfig.uploadUrl)
        console.log('请求已打开，URL:', uploadConfig.uploadUrl)
        console.log('请求方法:', 'POST')

        // 设置请求头
        if (uploadConfig.headers) {
          console.log('开始设置请求头...')
          Object.keys(uploadConfig.headers).forEach((key) => {
            xhr.setRequestHeader(key, uploadConfig.headers[key])
            console.log('设置请求头:', key, uploadConfig.headers[key])
          })
        } else {
          console.log('没有设置请求头')
        }

        // 设置超时
        xhr.timeout = uploadConfig.timeout || 30000
        console.log('设置超时时间:', xhr.timeout)

        console.log('准备发送FormData...')
        console.log('FormData大小:', formData.get('file')?.size || '未知')
        console.log('发送上传请求...')

        try {
          xhr.send(formData)
          console.log('XHR请求已发送')
        } catch (error) {
          console.error('发送XHR请求失败:', error)
          handleUploadError('发送请求失败: ' + error.message)
        }

        // 添加模拟进度，以防真实进度事件不触发
        let simulatedProgress = 0
        const progressInterval = setInterval(() => {
          if (simulatedProgress < 90) {
            simulatedProgress += 10
            updateProgress(simulatedProgress)
          }
        }, 500)

        // 在完成时清除模拟进度
        xhr.addEventListener('load', function () {
          clearInterval(progressInterval)
        })

        xhr.addEventListener('error', function () {
          clearInterval(progressInterval)
        })

        xhr.addEventListener('timeout', function () {
          clearInterval(progressInterval)
        })
      }

      // 更新进度
      function updateProgress(progress) {
        console.log('更新进度条:', progress + '%')
        if (progressFill) {
          progressFill.style.width = progress + '%'
          console.log('进度条宽度已设置为:', progress + '%')
        } else {
          console.error('进度条元素不存在!')
        }
      }

      // 处理上传成功
      function handleUploadSuccess(response) {
        uploadBtn.textContent = '上传成功'
        uploadBtn.style.backgroundColor = '#28a745'

        sendMessageToUniApp({
          type: 'UPLOAD_SUCCESS',
          data: response,
        })
      }

      // 处理上传错误
      function handleUploadError(error) {
        uploadBtn.disabled = false
        uploadBtn.textContent = '上传文件'
        uploadBtn.style.backgroundColor = '#007aff'

        showError(error)

        sendMessageToUniApp({
          type: 'UPLOAD_ERROR',
          data: error,
        })
      }

      // 显示错误
      function showError(message) {
        errorMessage.textContent = message
        errorMessage.classList.add('show')
      }

      // 隐藏错误
      function hideError() {
        errorMessage.classList.remove('show')
      }

      // 通知UniApp就绪
      function notifyUniAppReady() {
        console.log('通知UniApp就绪')
        sendMessageToUniApp({
          type: 'WEBVIEW_READY',
        })
      }

      // 发送消息到UniApp
      function sendMessageToUniApp(message) {
        console.log('尝试发送消息到UniApp:', message)

        // App/小程序
        if (window.uni && window.uni.postMessage) {
          console.log('使用window.uni.postMessage发送消息')
          try {
            window.uni.postMessage({
              data: message,
            })
          } catch (error) {
            console.error('window.uni.postMessage发送失败:', error)
          }
        }

        // H5
        if (window.parent) {
          console.log('使用window.parent.postMessage发送消息')
          try {
            window.parent.postMessage(message, '*')
          } catch (error) {
            console.error('window.parent.postMessage发送失败:', error)
          }
        }

        // 尝试其他方式发送消息
        try {
          if (window.webkit && window.webkit.messageHandlers) {
            console.log('使用webkit.messageHandlers发送消息')
            window.webkit.messageHandlers.postMessage.postMessage(message)
          }
        } catch (error) {
          console.error('webkit.messageHandlers发送失败:', error)
        }
      }

      // 接收UniApp消息
      window.addEventListener('message', function (event) {
        console.log('收到window消息:', event.data)
        const message = event.data

        if (message && message.type === 'INIT_CONFIG') {
          uploadConfig = message.data
          console.log('收到配置:', uploadConfig)
          // 每次收到消息都重新校验按钮状态
          updateUploadBtnStatus()
        }
      })

      // 兼容uni-app的消息接收方式
      if (window.uni && window.uni.onMessage) {
        console.log('设置uni.onMessage监听器')
        window.uni.onMessage(function (event) {
          console.log('收到uni消息:', event.data)
          const message = event.data

          if (message && message.type === 'INIT_CONFIG') {
            uploadConfig = message.data
            console.log('收到配置:', uploadConfig)
            // 每次收到消息都重新校验按钮状态
            updateUploadBtnStatus()
          }
        })
      } else {
        console.log('window.uni.onMessage不可用')
      }

      // 添加额外的消息监听，确保能接收到配置
      if (window.parent) {
        window.parent.addEventListener('message', function (event) {
          console.log('收到parent消息:', event.data)
          const message = event.data

          if (message && message.type === 'INIT_CONFIG') {
            uploadConfig = message.data
            console.log('收到配置:', uploadConfig)
            // 每次收到消息都重新校验按钮状态
            updateUploadBtnStatus()
          }
        })
      }

      // 添加调试信息
      console.log('PDF上传页面已加载')
      console.log('window.uni:', window.uni)
      console.log('uploadConfig初始值:', uploadConfig)

      // 测试消息发送
      setTimeout(() => {
        console.log('5秒后测试发送就绪消息')
        notifyUniAppReady()
      }, 5000)

      // 添加测试上传功能
      function testUpload() {
        console.log('=== 测试上传功能 ===')
        console.log('selectedFile:', selectedFile)
        console.log('uploadConfig:', uploadConfig)
        console.log('uploadUrl:', uploadConfig?.uploadUrl)

        if (selectedFile && uploadConfig && uploadConfig.uploadUrl) {
          console.log('开始测试上传')
          uploadFile(selectedFile)
        } else {
          console.log('测试上传条件不满足')
          console.log('selectedFile存在:', !!selectedFile)
          console.log('uploadConfig存在:', !!uploadConfig)
          console.log('uploadUrl存在:', !!(uploadConfig && uploadConfig.uploadUrl))
        }
      }

      // 自动触发上传测试（5秒后）
      setTimeout(() => {
        console.log('5秒后自动测试上传')
        if (selectedFile && uploadConfig && uploadConfig.uploadUrl) {
          console.log('自动触发上传测试')
          uploadFile(selectedFile)
        } else {
          console.log('自动测试条件不满足，跳过')
        }
      }, 5000)

      // 强制上传函数（绕过所有检查）
      function forceUpload() {
        console.log('=== 强制上传 ===')
        if (selectedFile) {
          console.log('强制上传文件:', selectedFile.name)
          uploadFile(selectedFile)
        } else {
          console.log('没有选择文件，无法强制上传')
        }
      }

      window.forceUpload = forceUpload

      // 将测试函数暴露到全局，方便调试
      window.testUpload = testUpload

      // 添加配置检查函数
      function checkConfig() {
        console.log('检查配置状态:')
        console.log('selectedFile:', selectedFile)
        console.log('uploadConfig:', uploadConfig)
        console.log('uploadUrl:', uploadConfig && uploadConfig.uploadUrl)
        console.log('按钮状态:', uploadBtn.disabled)
        updateUploadBtnStatus()
      }

      // 添加网络连接测试
      function testNetwork() {
        console.log('=== 测试网络连接 ===')
        const testUrl = 'https://www.easyzhipin.com/easyzhipin-api/attachment/uploadImgThum'

        const xhr = new XMLHttpRequest()
        xhr.open('GET', testUrl)
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            console.log('网络测试结果:', {
              status: xhr.status,
              statusText: xhr.statusText,
              responseText: xhr.responseText.substring(0, 100),
            })
          }
        }
        xhr.onerror = function () {
          console.error('网络测试失败')
        }
        xhr.send()
      }

      window.checkConfig = checkConfig
      window.testNetwork = testNetwork
    </script>
    <script>
      console.log('pdf-upload.html 脚本已加载')
    </script>
  </body>
</html>
