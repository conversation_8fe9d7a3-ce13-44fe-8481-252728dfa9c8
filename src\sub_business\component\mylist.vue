<template>
  <view class="p-[34rpx_30rpx_34rpx]">
    <view class="flex flex-col gap-20rpx">
      <view class="flex items-center gap-26rpx">
        <wd-img :src="avatarOne" width="72rpx" height="72rpx" round />
        <view class="flex flex-col gap-6rpx">
          <view class="c-#333333">
            <text class="text-28rpx font-500">{{ personalList.trueName }}</text>
            <text class="text-22rpx font-400 ml-6rpx">{{ personalList.expectedPositions }}</text>
          </view>
          <text class="c-#333333 text-22rpx line-clamp-1">
            {{
              [
                personOtherInfo.seekStatus,
                personOtherInfo.qualification,
                !(personalList?.lastWorkYears ?? 0)
                  ? '1年内'
                  : `${personalList?.lastWorkYears}年以上`,
                `${[formatToKilo(personalList.salaryExpectationStart), formatToKilo(personalList.salaryExpectationEnd)].filter(Boolean).join('-') || '面议'}`,
                personalList.major,
              ]
                .filter(Boolean)
                .join(' | ')
            }}
          </text>
        </view>
      </view>
      <view class="flex items-center gap-16rpx" v-if="personalList.lastWorkCompanyName">
        <wd-img :src="postMark" width="26rpx" height="26rpx" />
        <text class="c-#333333 text-22rpx">
          {{
            [personalList.lastWorkCompanyName, personalList.lastWorkPositionName]
              .filter(Boolean)
              .join(' · ')
          }}
        </text>
      </view>
    </view>
    <view class="flex flex-col gap-20rpx mt-10rpx">
      <text class="c-#333333 text-22rpx line-clamp-2" v-if="personalList?.myLights">
        {{ personalList.myLights }}
      </text>
      <view class="flex items-center flex-wrap gap-20rpx">
        <view
          class="bg-#F3F3F3 border-rd-6rpx h-36rpx min-w-150rpx px-20rpx center"
          v-for="(item, index) in splitToArray(personalList?.certificateNames)"
          :key="index"
        >
          <text class="c-#888888 text-22rpx">{{ item }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { splitToArray, formatToKilo } from '@/utils'
import { DICT_IDS } from '@/enum'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import avatarOne from '@/static/common/avatar/1.png'
import postMark from '@/static/common/post-mark.png'

interface propsInt {
  list: hrIndexResumeUserListInt
}
const props = withDefaults(defineProps<propsInt>(), {
  list: () => ({}) as hrIndexResumeUserListInt,
})
const { getDictLabel } = useDictionary()
const personOtherInfo = reactive({
  /** 学历 */
  qualification: '',
  /** 求职状态 */
  seekStatus: '',
})
const personalList = computed(() => props.list)

async function getQualification() {
  personOtherInfo.qualification = (await getDictLabel(
    DICT_IDS.EDUCATION_REQUIREMENT,
    personalList.value.qualification,
  )) as string
}
async function getSeekStatus() {
  personOtherInfo.seekStatus = (await getDictLabel(
    DICT_IDS.SEEK_STATUS,
    personalList.value.seekStatus,
  )) as string
}
onMounted(() => {
  getQualification()
  getSeekStatus()
})
</script>

<style lang="scss" scoped>
//
</style>
