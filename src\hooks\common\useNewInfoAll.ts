import { setCheckInfo } from '@/utils/storage'
import { USER_TYPE } from '@/enum'
import { newsInfo } from '@/interPost/login/index'

export const useNewInfoAll = () => {
  const { userIntel } = useUserInfo()
  const { initEaseMobIM, logoutEaseMobIM, loginEaseMobIM } = useEaseMobIM()
  const appHomeRoutes = { url: '/pages/home/<USER>', method: 'reLaunch' }

  /** 处理路由跳转 */
  const handleRouteNavigation = async (
    route: { url: string; method: string },
    needInitIM = false,
    isRoleSwitch = false,
  ) => {
    if (needInitIM) {
      if (isRoleSwitch) {
        logoutEaseMobIM()
        await loginEaseMobIM(userIntel.value.type)
      } else {
        await initEaseMobIM()
      }
    }
    uni[route.method]({ url: route.url })
  }
  /** 获取步骤路由配置 */
  const getStepRoutes = (userType: USER_TYPE) => {
    const commonRoutes = {
      '-999': {
        url: '/loginSetting/category/index',
        method: userType === USER_TYPE.APPLICANT ? 'navigateTo' : 'reLaunch',
      },
    }
    if (userType === USER_TYPE.APPLICANT) {
      return {
        ...commonRoutes,
        0: { url: '/loginSetting/createResume/biographicalOne', method: 'navigateTo' },
        1: { url: '/loginSetting/category/JobIntention', method: 'navigateTo' },
        2: appHomeRoutes,
        3: appHomeRoutes,
        4: appHomeRoutes,
        5: appHomeRoutes,
      }
    } else {
      return {
        ...commonRoutes,
        0: { url: '/loginSetting/companyJoin/companyInfo', method: 'reLaunch' },
        1: { url: '/loginSetting/companyJoin/recrIdent', method: 'reLaunch' },
        2: { url: '/loginSetting/companyJoin/jobCertificate', method: 'reLaunch' },
        3: appHomeRoutes,
        4: appHomeRoutes,
        5: appHomeRoutes,
      }
    }
  }

  /** 判断是否需要初始化IM */
  const needInitIM = (userType: USER_TYPE, step: number) => {
    const homeSteps = userType === USER_TYPE.APPLICANT ? [2, 3, 4, 5] : [3, 4, 5]
    return homeSteps.includes(step)
  }
  /**
   * 获取用户最新信息并根据步骤跳转
   * @param isRoleSwitch 是否是角色切换
   * @param forceStep 强制跳转，1表示直接跳转到首页
   */
  const newInfoStepPage = async (isRoleSwitch = false, forceStep?: number) => {
    if (forceStep === 1) {
      await handleRouteNavigation(appHomeRoutes, true, isRoleSwitch)
      return
    }
    const { data } = await newsInfo()
    if (!data.userStatus) {
      return
    }
    setCheckInfo(data)
    const { userLastLoginType: userType, completeStep: step } = data
    const stepRoutes = getStepRoutes(userType as USER_TYPE)
    const route = stepRoutes[step]
    if (route) {
      const shouldInitIM = needInitIM(userType as USER_TYPE, step)
      await handleRouteNavigation(route, shouldInitIM, isRoleSwitch)
    }
  }
  return {
    newInfoStepPage,
  }
}
