<template>
  <view class="flex flex-col gap-34rpx">
    <view
      class="flex items-center gap-20rpx"
      v-for="(item, key) in conversationList"
      :key="`news-list-${key}`"
      @click="toChatPage(item.conversationId)"
    >
      <view
        class="size-92rpx"
        :class="item?.isOnline ? 'p-6rpx rounded-50% border-1px border-solid border-[#06BC30]' : ''"
      >
        <wd-img width="100%" height="100%" round :src="item?.avatar" />
      </view>
      <view class="flex flex-col gap-6rpx flex-1">
        <view class="flex items-center gap-4rpx">
          <view class="flex-1 flex items-center">
            <text class="c-#333333 text-28rpx font-500 line-clamp-1">
              {{ item?.nickname }}
            </text>
          </view>
          <view class="flex items-center">
            <text class="c-#888888 text-24rpx">
              {{
                formatSmartTime(
                  item.lastMessage && 'time' in item.lastMessage ? item.lastMessage.time : '',
                )
              }}
            </text>
            <view class="bg-#FF3333 size-32rpx center border-rd-50%" v-if="!!item.unReadCount">
              <text class="text-20rpx c-#ffffff">
                {{ item.unReadCount > 99 ? '99+' : item.unReadCount }}
              </text>
            </view>
          </view>
        </view>
        <text class="line-clamp-1 c-#888888 text-24rpx ml--10rpx flex-1">
          {{ getLastTypeMessage(item.lastMessage as any) }}
        </text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { formatSmartTime } from '@/utils'

const $emits = defineEmits<{
  (e: 'chat', id: string): void
}>()

const { conversationList, getLastTypeMessage } = useIMConversation()

const toChatPage = (id: string) => {
  $emits('chat', id)
}
</script>

<style lang="scss" scoped>
//
</style>
