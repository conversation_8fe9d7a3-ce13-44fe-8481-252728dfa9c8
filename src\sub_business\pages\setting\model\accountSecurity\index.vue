<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="账号安全"></CustomNavBar>
    </template>
    <view class="setting">
      <!-- 修改手机号 -->
      <view class="setting-list border-b" @click="goPhoneChange">
        <view class="row-main flex-between">
          <view class="text-32rpx">修改手机号</view>
          <view class="phone-arrow">
            <text class="phone-text">177 **** 7777</text>
            <wd-icon
              name="chevron-right"
              size="20px"
              color="#888888"
              class="arrow-right-icon"
            ></wd-icon>
          </view>
        </view>
        <view class="tip-text">修改成功后，可通过新手机号登录易直聘</view>
      </view>
      <!-- 修改密码 -->
      <!-- <view class="setting-list border-b" @click="goPasswordChange">
        <view class="row-main flex-between">
          <view class="text-32rpx">修改密码</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const goPhoneChange = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/PhoneChange',
  })
}
// 修改密码
// const goPasswordChange = () => {
//   uni.navigateTo({
//     url: '/sub_business/pages/setting/model/accountSecurity/PasswordChange',
//   })
// }
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx 10rpx 20rpx;
  }
  .row-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .phone-arrow {
    display: flex;
    align-items: center;
    .phone-text {
      margin-right: 10rpx;
      font-size: 28rpx;
      color: #888;
    }
  }
  .tip-text {
    margin-top: 8rpx;
    margin-left: 2rpx;
    font-size: 22rpx;
    line-height: 1.4;
    color: #999999;
  }
}
</style>
