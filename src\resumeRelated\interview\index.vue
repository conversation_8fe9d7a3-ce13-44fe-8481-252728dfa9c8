<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="待面试">
      <template #right>
        <view class="text-28rpx c-#000" @click="goHistory">历史面试</view>
      </template>
    </CustomNavBar>
    <!-- <view class="page-time flex items-center p-l-40rpx p-r-40rpx p-t-60rpx">
      <view class="text-100rpx font-600 c-#000">11</view>
      <view>
        <view class="text-32rpx c-#555">月</view>
        <view class="text-32rpx c-#555">2025年</view>
      </view>
    </view> -->

    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view class="pageList">
        <view class="c-#888 text-32rpx p-b-20rpx p-l-40rpx" v-if="pageData.length > 0">时间</view>
        <view class="pageList-item flex items-start" v-for="(item, index) in pageData" :key="index">
          <view class="pageList-item-left">
            <view class="c-#000 text-24rpx">
              {{ item.agreeTime.slice(5, 7) }}月{{ item.agreeTime.slice(8, 10) }}日
            </view>
            <view class="c-#000 text-34rpx text-right">{{ item.agreeTime.slice(11, 13) }}点</view>
          </view>
          <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
            <view class="pageList-item-right-card flex items-center">
              <view class="w-90rpx">
                <image
                  class="w-76rpx h-76rpx rounded-full"
                  v-if="item.sex === 1"
                  :src="item.headImgUrl ? item.headImgUrl : '/static/header/jobhunting1.png'"
                  mode="aspectFill"
                ></image>
                <image
                  v-else
                  class="w-76rpx h-76rpx rounded-full"
                  :src="item.headImgUrl ? item.headImgUrl : '/static/header/jobhunting2.png'"
                  mode="aspectFill"
                ></image>
              </view>

              <view class="flex-1">
                <view class="c-#fff text-28rpx p-b-5rpx u-line-1 w-340rpx">
                  {{ item.name }}
                </view>
                <view class="flex justify-between">
                  <view class="c-#fff text-24rpx">{{ item.positionName }}</view>
                  <view class="c-#fff text-24rpx">
                    {{ item.workSalaryStart }}-{{ item.workSalaryEnd }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryWaitMeetingList } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { getCustomBar } from '@/utils/storage'
const { pagingRef, pageData, pageStyle } = usePaging({
  style: {
    padding: '80rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '40rpx',
  },
})
const customBar = ref(null)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async () => {
  const res: any = await queryWaitMeetingList()
  if (res.code === 0) {
    res.data &&
      res.data.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryStart =
          ele.workSalaryStart === 0 ? '面议' : numberTokw(ele.workSalaryStart + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '面议' : numberTokw(ele.workSalaryEnd + '')
      })

    pagingRef.value.complete(res.data)
  }
}
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  background: #4399ff;
  border-radius: 20rpx;
}
</style>
