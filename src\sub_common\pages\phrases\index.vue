<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '常用语',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :refresher-enabled="false" :paging-style="pageStyle" safe-area-inset-bottom>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          @click-left="handleClickLeft"
          @click-right="handleClickRight"
          custom-class="px-25rpx"
        >
          <template #title>
            <view class="center">
              <view class="w-300rpx">
                <wd-tabs
                  v-model="phrasesTabsStatus"
                  line-width="130rpx"
                  line-height="6rpx"
                  color="#333333"
                  inactive-color="#666666"
                >
                  <wd-tab
                    v-for="item in phrasesTabsList"
                    :key="item.name"
                    :title="`${item.label}`"
                    :name="item.name"
                  />
                </wd-tabs>
              </view>
            </view>
          </template>
          <template #right>
            <text class="c-#333333 text-28rpx">排序</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-30rpx px-50rpx mt-44rpx">
      <wd-config-provider :themeVars="themeVars">
        <common-card>
          <view class="px-36rpx py-18rpx flex flex-col gap-16rpx">
            <text class="c-#333333 text-24rpx whitespace-pre-wrap line-height-44rpx">
              老板你好！非常想加入你们，可以看下我的资料。期待回复。
            </text>
            <view class="flex items-center">
              <view class="flex-1">
                <wd-checkbox v-model="firstReplyTip" checked-color="#075EFF">
                  <text class="c-#075EFF text-24rpx">设为默认</text>
                </wd-checkbox>
              </view>
              <view class="flex items-center gap-44rpx">
                <wd-icon :name="phrasesEdit" size="40rpx" />
                <wd-icon :name="phrasesDelete" size="40rpx" />
              </view>
            </view>
          </view>
        </common-card>
      </wd-config-provider>
    </view>
    <template #bottom>
      <view class="px-86rpx py-20rpx">
        <wd-button size="large" custom-class="w-full !h-92rpx !bg-#075EFF">
          <text class="text-28rpx c-#FFFFFF">设为默认</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import commonCard from '@/components/common/common-card.vue'
import phrasesEdit from '@/sub_common/static/phrases/phrases-edit.png'
import phrasesDelete from '@/sub_common/static/phrases/phrases-delete.png'

const { pageParams } = usePagePeriod()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: ' transparent',
  navbarArrowSize: '30rpx',
  tabsNavLineBgColor: 'linear-gradient( 270deg, #FFC2C2 0%, #DDDCFF 100%)',
  tabsNavFs: '28rpx',
  checkboxLabelMargin: '6rpx',
}
const firstReplyTip = ref(false)
const phrasesTabsStatus = ref(0)
const phrasesTabsList = [
  {
    label: '常用语',
    name: 0,
  },
  {
    label: '招呼语',
    name: 1,
  },
]
function handleClickRight() {
  uni.showToast({
    title: '排序功能待开发',
    icon: 'none',
  })
}
function handleClickLeft() {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 6px;
  }
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    font-weight: 500;
    &.is-active {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style>
