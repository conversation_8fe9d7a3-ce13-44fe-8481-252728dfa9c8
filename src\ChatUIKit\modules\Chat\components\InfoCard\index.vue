<template>
  <template v-if="userRoleIsBusiness">
    <view class="my-30rpx"></view>
  </template>
  <template v-else>
    <view
      class="flex items-center c-#333333 text-26rpx my-30rpx bg-white rounded-[40rpx] h-156rpx px-42rpx gap-40rpx"
    >
      <view class="flex flex-col flex-justify-center gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoPosition" width="34rpx" height="34rpx" />
          <text class="line-clamp-1 flex">{{ customCardInfo.positionName }}</text>
        </view>
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoWelfare" width="34rpx" height="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customCardInfo.positionBenefitList?.join(' · ') }}
          </text>
        </view>
      </view>
      <view class="flex flex-col flex-justify-center gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoCompanySalary" width="34rpx" height="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{
              [
                `${[formatToKilo(customCardInfo.workSalaryBegin), formatToKilo(customCardInfo.workSalaryEnd)].filter(Boolean).join('-') || '面议'}`,
                `${customCardInfo.salaryMonths > 12 ? customCardInfo.salaryMonths + '薪' : ''}`,
              ]
                .filter(Boolean)
                .join(' · ')
            }}
          </text>
        </view>
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoCompanyAddress" width="34rpx" height="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ formatAddress }}
          </text>
        </view>
      </view>
    </view>
  </template>
</template>

<script lang="ts" setup>
import { formatToKilo } from '@/utils'
import { positionInfoQueryIMCardInfoById } from '@/service/positionInfo'
import infoPosition from '@/ChatUIKit/static/info-position.png'
import infoCompanyAddress from '@/ChatUIKit/static/info-company-address.png'
import infoCompanySalary from '@/ChatUIKit/static/info-company-salary.png'
import infoWelfare from '@/ChatUIKit/static/info-welfare.png'
import { CommonUtil } from 'wot-design-uni'

interface propsInterface {
  ext: Api.IM.UserBusinessExtInfo
}

defineOptions({
  name: 'ChatInfoCard',
})

const props = withDefaults(defineProps<propsInterface>(), {})
const propsExt = computed(() => props.ext)

const { userIntel, getUserIsLogin, userRoleIsBusiness } = useUserInfo()
const { customCardInfo } = useIMConversation()

const formatAddress = computed(() => {
  const { provinceName, cityName, districtName } = customCardInfo.value
  const isDirectMunicipality = provinceName === cityName
  const addressParts = isDirectMunicipality
    ? [provinceName, districtName]
    : [provinceName, cityName, districtName]
  return addressParts.filter(Boolean).join('')
})
async function fetchCardInfo() {
  try {
    const hruserId = userRoleIsBusiness.value ? userIntel.value.userId : propsExt.value.hrUserId
    const userId = userRoleIsBusiness.value ? propsExt.value.hrUserId : userIntel.value.userId
    const { data } = await positionInfoQueryIMCardInfoById(
      {
        userId,
        hruserId,
      },
      {
        custom: {
          toast: true,
          catch: true,
        },
      },
    )
    customCardInfo.value = data
  } catch (error) {
    console.error('Error fetching card info:', error)
  }
}
watch(
  [() => getUserIsLogin.value],
  ([login]) => {
    if (login) {
      fetchCardInfo()
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
//
</style>
