<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar>
      <template #left>
        <wd-icon
          @click="changeIdentFun"
          name="arrow-left"
          class="back-button"
          color="#000"
          size="20"
        />
      </template>
    </CustomNavBar>
    <view class="box" :style="{ paddingTop: statusBar * 2 + 'rpx' }">
      <view class="box_flex box_top" @click="handleJump(USER_TYPE.APPLICANT)">
        <view class="box_img box_img_1"></view>
        <view class="box_flex_row">
          <view class="box_name">我要找工作</view>
        </view>
      </view>
      <view class="box_flex" @click="handleJump(USER_TYPE.HR)">
        <view class="box_img box_img_2"></view>
        <view class="box_flex_row">
          <view class="box_name">我要招人才</view>
        </view>
      </view>
    </view>
    <CommonLink></CommonLink>
  </view>
</template>

<script setup lang="ts">
import { USER_TYPE } from '@/enum'
import debounce from '@/utils/debounce'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CommonLink from '@/components/CommonLink/CommonLink.vue'
import { getStatusBar } from '@/utils/storage'
import { updateChooseType } from '@/interPost/biographical'
const { changeIdent } = useChangeIdent()
const { setUserRoleType } = useUserInfo()

const statusBar = ref(0)
// 切换身份
const changeIdentFun = async () => {
  debounce(changeIdent(), 1000)
}
async function handleJump(lastLoginType: USER_TYPE) {
  const url = {
    [USER_TYPE.APPLICANT]: '/loginSetting/createResume/biographicalOne',
    [USER_TYPE.HR]: '/loginSetting/companyJoin/companyInfo',
  }[lastLoginType]
  await updateChooseType({
    lastLoginType,
  })
  setUserRoleType(lastLoginType)
  uni.navigateTo({
    url,
  })
}

onLoad(() => {
  statusBar.value = getStatusBar()
})
</script>
<style lang="scss" scoped>
.box {
  box-sizing: border-box;
  width: 100%;
  padding: 110rpx;
}

.box_flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 380rpx;
  padding-bottom: 130rpx;
  margin: auto;
}
.box_img {
  width: 324rpx;
  height: 324rpx;
}
.box_top {
  padding-top: 120rpx;
}

.box_img_1 {
  background-image: url('/static/img/<EMAIL>');
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 100% 100%;
}

.box_img_2 {
  background-image: url('/static/img/<EMAIL>');
  background-repeat: no-repeat;
  background-position: 100% 100%;
  background-size: 100% 100%;
}

.box_flex_row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 20rpx;

  .box_name {
    font-size: 32rpx;
    font-weight: 700;
    color: #000000;
    text-align: center;
  }

  .box_icon {
    position: absolute;
    right: 60rpx;
    float: right;

    .box_right_icon {
      width: 65rpx;
      height: 65rpx;
    }
  }
}
</style>
